"use client";

import {
  getSmoothStepPath,
  Position,
  type Edge,
  type Node,
} from "@xyflow/react";

export interface LayoutOptions {
  horizontalSpacing?: number;
  verticalSpacing?: number;
  startX?: number;
  startY?: number;
  alignByLayer?: boolean;
  handleOrientation?: "horizontal" | "vertical" | "auto";
  animationDuration?: number;
  minimizeEdgeCrossings?: boolean;
  edgeCrossingIterations?: number;
  dynamicSpacing?: boolean;
  edgeNodeSpacing?: number;
  edgeNodeCollisionIterations?: number;
  edgeEdgeSpacing?: number;
  edgeEdgeCollisionIterations?: number;
  onComplete?: (finalPositions: Map<string, { x: number; y: number }>) => void;
}

export interface AutoLayoutResult {
  positions: Map<string, { x: number; y: number }>;
  orientation: "horizontal" | "vertical";
}

/**
 * Detects the predominant handle orientation in the workflow
 */
export const detectHandleOrientation = (
  nodes: Node[],
): "horizontal" | "vertical" => {
  if (nodes.length === 0) return "horizontal";

  // For now, default to horizontal layout
  // This can be enhanced to detect based on node connections
  return "horizontal";
};

/**
 * Calculate the barycenter (average position) of connected nodes in adjacent levels
 */
const calculateBarycenter = (
  nodeId: string,
  level: number,
  levelNodes: Map<number, string[]>,
  adjacencyList: Map<string, string[]>,
  reverseAdjacencyList: Map<string, string[]>,
  orientation: "horizontal" | "vertical",
): number => {
  const currentLevelNodes = levelNodes.get(level) || [];
  const currentIndex = currentLevelNodes.indexOf(nodeId);

  // Get connected nodes from previous and next levels
  const connectedNodes: string[] = [];

  // Add nodes from previous level (sources)
  const prevLevelNodes = levelNodes.get(level - 1) || [];
  const sources = reverseAdjacencyList.get(nodeId) || [];
  sources.forEach((sourceId) => {
    if (prevLevelNodes.includes(sourceId)) {
      connectedNodes.push(sourceId);
    }
  });

  // Add nodes from next level (targets)
  const nextLevelNodes = levelNodes.get(level + 1) || [];
  const targets = adjacencyList.get(nodeId) || [];
  targets.forEach((targetId) => {
    if (nextLevelNodes.includes(targetId)) {
      connectedNodes.push(targetId);
    }
  });

  if (connectedNodes.length === 0) {
    return currentIndex; // Return current position if no connections
  }

  // Calculate average position of connected nodes
  let totalPosition = 0;
  connectedNodes.forEach((connectedId) => {
    // Find the position of connected node in its level
    for (const [connectedLevel, nodes] of levelNodes.entries()) {
      const connectedIndex = nodes.indexOf(connectedId);
      if (connectedIndex !== -1) {
        totalPosition += connectedIndex;
        break;
      }
    }
  });

  return totalPosition / connectedNodes.length;
};

/**
 * Minimize edge crossings using barycenter heuristic
 */
const minimizeEdgeCrossingsWithBarycenter = (
  levelNodes: Map<number, string[]>,
  adjacencyList: Map<string, string[]>,
  reverseAdjacencyList: Map<string, string[]>,
  orientation: "horizontal" | "vertical",
  iterations: number = 3,
): Map<number, string[]> => {
  const optimizedLevelNodes = new Map<number, string[]>();

  // Copy original level nodes
  levelNodes.forEach((nodes, level) => {
    optimizedLevelNodes.set(level, [...nodes]);
  });

  // Apply barycenter heuristic for specified iterations
  for (let iter = 0; iter < iterations; iter++) {
    // Process levels in alternating directions
    const levels = Array.from(optimizedLevelNodes.keys()).sort((a, b) => a - b);

    for (const level of levels) {
      const nodes = optimizedLevelNodes.get(level) || [];
      if (nodes.length <= 1) continue;

      // Calculate barycenter for each node
      const nodeBarycenter = nodes.map((nodeId) => ({
        nodeId,
        barycenter: calculateBarycenter(
          nodeId,
          level,
          optimizedLevelNodes,
          adjacencyList,
          reverseAdjacencyList,
          orientation,
        ),
      }));

      // Sort nodes by barycenter
      nodeBarycenter.sort((a, b) => a.barycenter - b.barycenter);

      // Update level with sorted nodes
      optimizedLevelNodes.set(
        level,
        nodeBarycenter.map((item) => item.nodeId),
      );
    }
  }

  return optimizedLevelNodes;
};

/**
 * Calculate dynamic spacing based on edge density between levels
 */
const calculateDynamicSpacing = (
  levelNodes: Map<number, string[]>,
  adjacencyList: Map<string, string[]>,
  baseSpacing: number,
  orientation: "horizontal" | "vertical",
): number => {
  let maxEdgeCount = 0;

  // Count edges between adjacent levels
  for (const [level, nodes] of levelNodes.entries()) {
    const nextLevelNodes = levelNodes.get(level + 1);
    if (!nextLevelNodes) continue;

    let edgeCount = 0;
    nodes.forEach((nodeId) => {
      const targets = adjacencyList.get(nodeId) || [];
      targets.forEach((targetId) => {
        if (nextLevelNodes.includes(targetId)) {
          edgeCount++;
        }
      });
    });

    maxEdgeCount = Math.max(maxEdgeCount, edgeCount);
  }

  // Increase spacing based on edge density
  const spacingMultiplier = Math.max(1, Math.sqrt(maxEdgeCount / 3));
  return Math.round(baseSpacing * spacingMultiplier);
};

/**
 * Node dimensions and bounding box utilities
 */
interface NodeBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

// Default node dimensions based on CustomNode component
const DEFAULT_NODE_WIDTH = 240; // min-w-[240px] from CustomNode
const DEFAULT_NODE_HEIGHT = 80; // Estimated height with padding

/**
 * Get bounding box for a node with padding
 */
const getNodeBounds = (
  nodeId: string,
  positions: Map<string, { x: number; y: number }>,
  padding: number = 0,
): NodeBounds => {
  const position = positions.get(nodeId);
  if (!position) {
    return {
      x: 0,
      y: 0,
      width: DEFAULT_NODE_WIDTH,
      height: DEFAULT_NODE_HEIGHT,
    };
  }

  return {
    x: position.x - padding,
    y: position.y - padding,
    width: DEFAULT_NODE_WIDTH + padding * 2,
    height: DEFAULT_NODE_HEIGHT + padding * 2,
  };
};

/**
 * Parse SVG path string to extract line segments for collision detection
 */
const parseSVGPathToSegments = (
  pathString: string,
): Array<{ x1: number; y1: number; x2: number; y2: number }> => {
  const segments: Array<{ x1: number; y1: number; x2: number; y2: number }> =
    [];

  // Simple SVG path parser for smooth step paths
  // This handles M (moveTo), L (lineTo), C (curveTo), and Q (quadraticCurveTo) commands
  const commands = pathString.match(/[MLCQZ][^MLCQZ]*/g) || [];

  let currentX = 0;
  let currentY = 0;

  commands.forEach((command) => {
    const type = command[0];
    const coords = command
      .slice(1)
      .trim()
      .split(/[\s,]+/)
      .map(Number)
      .filter((n) => !isNaN(n));

    switch (type) {
      case "M": // Move to
        if (coords.length >= 2) {
          currentX = coords[0];
          currentY = coords[1];
        }
        break;

      case "L": // Line to
        if (coords.length >= 2) {
          segments.push({
            x1: currentX,
            y1: currentY,
            x2: coords[0],
            y2: coords[1],
          });
          currentX = coords[0];
          currentY = coords[1];
        }
        break;

      case "C": // Cubic Bezier curve - approximate with multiple line segments
        if (coords.length >= 6) {
          const steps = 20; // Increased segments for better collision detection
          const startX = currentX;
          const startY = currentY;
          const cp1x = coords[0];
          const cp1y = coords[1];
          const cp2x = coords[2];
          const cp2y = coords[3];
          const endX = coords[4];
          const endY = coords[5];

          for (let i = 1; i <= steps; i++) {
            const t = i / steps;
            const prevT = (i - 1) / steps;

            // Cubic Bezier formula
            const prevX =
              Math.pow(1 - prevT, 3) * startX +
              3 * Math.pow(1 - prevT, 2) * prevT * cp1x +
              3 * (1 - prevT) * Math.pow(prevT, 2) * cp2x +
              Math.pow(prevT, 3) * endX;
            const prevY =
              Math.pow(1 - prevT, 3) * startY +
              3 * Math.pow(1 - prevT, 2) * prevT * cp1y +
              3 * (1 - prevT) * Math.pow(prevT, 2) * cp2y +
              Math.pow(prevT, 3) * endY;

            const currX =
              Math.pow(1 - t, 3) * startX +
              3 * Math.pow(1 - t, 2) * t * cp1x +
              3 * (1 - t) * Math.pow(t, 2) * cp2x +
              Math.pow(t, 3) * endX;
            const currY =
              Math.pow(1 - t, 3) * startY +
              3 * Math.pow(1 - t, 2) * t * cp1y +
              3 * (1 - t) * Math.pow(t, 2) * cp2y +
              Math.pow(t, 3) * endY;

            if (i === 1) {
              segments.push({ x1: startX, y1: startY, x2: currX, y2: currY });
            } else {
              segments.push({ x1: prevX, y1: prevY, x2: currX, y2: currY });
            }
          }

          currentX = endX;
          currentY = endY;
        }
        break;

      case "Q": // Quadratic Bezier curve - approximate with multiple line segments
        if (coords.length >= 4) {
          const steps = 15; // Increased segments for better collision detection
          const startX = currentX;
          const startY = currentY;
          const cpx = coords[0];
          const cpy = coords[1];
          const endX = coords[2];
          const endY = coords[3];

          for (let i = 1; i <= steps; i++) {
            const t = i / steps;
            const prevT = (i - 1) / steps;

            // Quadratic Bezier formula
            const prevX =
              Math.pow(1 - prevT, 2) * startX +
              2 * (1 - prevT) * prevT * cpx +
              Math.pow(prevT, 2) * endX;
            const prevY =
              Math.pow(1 - prevT, 2) * startY +
              2 * (1 - prevT) * prevT * cpy +
              Math.pow(prevT, 2) * endY;

            const currX =
              Math.pow(1 - t, 2) * startX +
              2 * (1 - t) * t * cpx +
              Math.pow(t, 2) * endX;
            const currY =
              Math.pow(1 - t, 2) * startY +
              2 * (1 - t) * t * cpy +
              Math.pow(t, 2) * endY;

            if (i === 1) {
              segments.push({ x1: startX, y1: startY, x2: currX, y2: currY });
            } else {
              segments.push({ x1: prevX, y1: prevY, x2: currX, y2: currY });
            }
          }

          currentX = endX;
          currentY = endY;
        }
        break;
    }
  });

  return segments;
};

/**
 * Calculate edge path segments for collision detection using actual SVG path
 */
const getEdgePathSegments = (
  sourceX: number,
  sourceY: number,
  targetX: number,
  targetY: number,
  orientation: "horizontal" | "vertical",
): Array<{ x1: number; y1: number; x2: number; y2: number }> => {
  const isHorizontal = orientation === "horizontal";
  const offset = isHorizontal ? 30 : 20;

  // Get the actual smooth step path that matches the rendered edge
  const [edgePath] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition: isHorizontal ? Position.Right : Position.Bottom,
    targetX,
    targetY,
    targetPosition: isHorizontal ? Position.Left : Position.Top,
    borderRadius: 8,
    offset,
  });

  // Parse the actual SVG path to get accurate segments
  return parseSVGPathToSegments(edgePath);
};

/**
 * Check if a line segment intersects with a rectangle (enhanced version)
 */
const lineIntersectsRect = (
  line: { x1: number; y1: number; x2: number; y2: number },
  rect: NodeBounds,
): boolean => {
  const { x1, y1, x2, y2 } = line;
  const { x, y, width, height } = rect;

  // Check if line endpoints are inside rectangle
  const p1Inside = x1 >= x && x1 <= x + width && y1 >= y && y1 <= y + height;
  const p2Inside = x2 >= x && x2 <= x + width && y2 >= y && y2 <= y + height;

  if (p1Inside || p2Inside) return true;

  // Check line intersection with rectangle edges
  const rectLines = [
    { x1: x, y1: y, x2: x + width, y2: y }, // Top edge
    { x1: x + width, y1: y, x2: x + width, y2: y + height }, // Right edge
    { x1: x + width, y1: y + height, x2: x, y2: y + height }, // Bottom edge
    { x1: x, y1: y + height, x2: x, y2: y }, // Left edge
  ];

  // Check intersection with each rectangle edge
  const hasIntersection = rectLines.some((rectLine) =>
    linesIntersect(line, rectLine),
  );

  // Additional check: if line passes very close to rectangle, consider it an intersection
  if (!hasIntersection) {
    const minDistanceToRect = calculateLineToRectDistance(line, rect);
    return minDistanceToRect < 5; // Consider lines within 5px as intersecting
  }

  return hasIntersection;
};

/**
 * Calculate minimum distance from a line segment to a rectangle
 */
const calculateLineToRectDistance = (
  line: { x1: number; y1: number; x2: number; y2: number },
  rect: NodeBounds,
): number => {
  const { x1, y1, x2, y2 } = line;
  const { x, y, width, height } = rect;

  // Helper function to calculate distance from point to line segment
  const pointToLineDistance = (px: number, py: number): number => {
    const dx = x2 - x1;
    const dy = y2 - y1;
    const length = Math.sqrt(dx * dx + dy * dy);

    if (length === 0) {
      return Math.sqrt((px - x1) * (px - x1) + (py - y1) * (py - y1));
    }

    const t = Math.max(
      0,
      Math.min(1, ((px - x1) * dx + (py - y1) * dy) / (length * length)),
    );
    const closestX = x1 + t * dx;
    const closestY = y1 + t * dy;

    return Math.sqrt(
      (px - closestX) * (px - closestX) + (py - closestY) * (py - closestY),
    );
  };

  // Check distance from line to all rectangle corners
  const corners = [
    { x, y },
    { x: x + width, y },
    { x: x + width, y: y + height },
    { x, y: y + height },
  ];

  let minDistance = Infinity;
  corners.forEach((corner) => {
    const distance = pointToLineDistance(corner.x, corner.y);
    minDistance = Math.min(minDistance, distance);
  });

  return minDistance;
};

/**
 * Check if two line segments intersect
 */
const linesIntersect = (
  line1: { x1: number; y1: number; x2: number; y2: number },
  line2: { x1: number; y1: number; x2: number; y2: number },
): boolean => {
  const { x1: x1a, y1: y1a, x2: x2a, y2: y2a } = line1;
  const { x1: x1b, y1: y1b, x2: x2b, y2: y2b } = line2;

  const denom = (y2b - y1b) * (x2a - x1a) - (x2b - x1b) * (y2a - y1a);
  if (denom === 0) return false; // Lines are parallel

  const ua = ((x2b - x1b) * (y1a - y1b) - (y2b - y1b) * (x1a - x1b)) / denom;
  const ub = ((x2a - x1a) * (y1a - y1b) - (y2a - y1a) * (x1a - x1b)) / denom;

  return ua >= 0 && ua <= 1 && ub >= 0 && ub <= 1;
};

/**
 * Calculate distance from a node to the nearest node in a specific direction
 */
const calculateDistanceToNearestNode = (
  nodeId: string,
  direction: "up" | "down" | "left" | "right",
  positions: Map<string, { x: number; y: number }>,
  nodes: Node[],
  minSpacing: number,
): number => {
  const nodePos = positions.get(nodeId);
  if (!nodePos) return Infinity;

  const nodeBounds = getNodeBounds(nodeId, positions);
  let minDistance = Infinity;

  nodes.forEach((otherNode) => {
    if (otherNode.id === nodeId) return;

    const otherBounds = getNodeBounds(otherNode.id, positions);
    let distance = Infinity;

    switch (direction) {
      case "up":
        if (otherBounds.y + otherBounds.height <= nodeBounds.y) {
          distance = nodeBounds.y - (otherBounds.y + otherBounds.height);
        }
        break;
      case "down":
        if (otherBounds.y >= nodeBounds.y + nodeBounds.height) {
          distance = otherBounds.y - (nodeBounds.y + nodeBounds.height);
        }
        break;
      case "left":
        if (otherBounds.x + otherBounds.width <= nodeBounds.x) {
          distance = nodeBounds.x - (otherBounds.x + otherBounds.width);
        }
        break;
      case "right":
        if (otherBounds.x >= nodeBounds.x + nodeBounds.width) {
          distance = otherBounds.x - (nodeBounds.x + nodeBounds.width);
        }
        break;
    }

    minDistance = Math.min(minDistance, distance);
  });

  return minDistance;
};

/**
 * Calculate distance from a node to the nearest edge in a specific direction
 */
const calculateDistanceToNearestEdge = (
  nodeId: string,
  direction: "up" | "down" | "left" | "right",
  positions: Map<string, { x: number; y: number }>,
  edges: Edge[],
  orientation: "horizontal" | "vertical",
  minSpacing: number,
): number => {
  const nodePos = positions.get(nodeId);
  if (!nodePos) return Infinity;

  const nodeBounds = getNodeBounds(nodeId, positions);
  let minDistance = Infinity;

  edges.forEach((edge) => {
    if (edge.source === nodeId || edge.target === nodeId) return;

    const sourcePos = positions.get(edge.source);
    const targetPos = positions.get(edge.target);
    if (!sourcePos || !targetPos) return;

    const sourceCenterX = sourcePos.x + DEFAULT_NODE_WIDTH / 2;
    const sourceCenterY = sourcePos.y + DEFAULT_NODE_HEIGHT / 2;
    const targetCenterX = targetPos.x + DEFAULT_NODE_WIDTH / 2;
    const targetCenterY = targetPos.y + DEFAULT_NODE_HEIGHT / 2;

    const segments = getEdgePathSegments(
      sourceCenterX,
      sourceCenterY,
      targetCenterX,
      targetCenterY,
      orientation,
    );

    segments.forEach((segment) => {
      let distance = Infinity;

      switch (direction) {
        case "up":
          if (Math.max(segment.y1, segment.y2) <= nodeBounds.y) {
            distance = nodeBounds.y - Math.max(segment.y1, segment.y2);
          }
          break;
        case "down":
          if (
            Math.min(segment.y1, segment.y2) >=
            nodeBounds.y + nodeBounds.height
          ) {
            distance =
              Math.min(segment.y1, segment.y2) -
              (nodeBounds.y + nodeBounds.height);
          }
          break;
        case "left":
          if (Math.max(segment.x1, segment.x2) <= nodeBounds.x) {
            distance = nodeBounds.x - Math.max(segment.x1, segment.x2);
          }
          break;
        case "right":
          if (
            Math.min(segment.x1, segment.x2) >=
            nodeBounds.x + nodeBounds.width
          ) {
            distance =
              Math.min(segment.x1, segment.x2) -
              (nodeBounds.x + nodeBounds.width);
          }
          break;
      }

      minDistance = Math.min(minDistance, distance);
    });
  });

  return minDistance;
};

/**
 * Find the optimal direction to move a node to avoid collisions
 */
const findOptimalDirection = (
  nodeId: string,
  positions: Map<string, { x: number; y: number }>,
  nodes: Node[],
  edges: Edge[],
  orientation: "horizontal" | "vertical",
  minSpacing: number,
): { direction: "up" | "down" | "left" | "right"; clearance: number } => {
  const directions: Array<"up" | "down" | "left" | "right"> = [
    "up",
    "down",
    "left",
    "right",
  ];
  let bestDirection: "up" | "down" | "left" | "right" = "right";
  let maxClearance = -1;

  directions.forEach((direction) => {
    const nodeDistance = calculateDistanceToNearestNode(
      nodeId,
      direction,
      positions,
      nodes,
      minSpacing,
    );
    const edgeDistance = calculateDistanceToNearestEdge(
      nodeId,
      direction,
      positions,
      edges,
      orientation,
      minSpacing,
    );
    const clearance = Math.min(nodeDistance, edgeDistance);

    if (clearance > maxClearance) {
      maxClearance = clearance;
      bestDirection = direction;
    }
  });

  return { direction: bestDirection, clearance: maxClearance };
};

/**
 * Detect collisions between nodes
 */
const detectNodeNodeCollisions = (
  nodes: Node[],
  positions: Map<string, { x: number; y: number }>,
  minSpacing: number,
): Array<{
  nodeId1: string;
  nodeId2: string;
  adjustmentVector1: { x: number; y: number };
  adjustmentVector2: { x: number; y: number };
}> => {
  const collisions: Array<{
    nodeId1: string;
    nodeId2: string;
    adjustmentVector1: { x: number; y: number };
    adjustmentVector2: { x: number; y: number };
  }> = [];

  for (let i = 0; i < nodes.length; i++) {
    for (let j = i + 1; j < nodes.length; j++) {
      const node1 = nodes[i];
      const node2 = nodes[j];

      const bounds1 = getNodeBounds(node1.id, positions, minSpacing / 2);
      const bounds2 = getNodeBounds(node2.id, positions, minSpacing / 2);

      // Check if nodes overlap
      const overlaps = !(
        bounds1.x + bounds1.width <= bounds2.x ||
        bounds2.x + bounds2.width <= bounds1.x ||
        bounds1.y + bounds1.height <= bounds2.y ||
        bounds2.y + bounds2.height <= bounds1.y
      );

      if (overlaps) {
        // Calculate overlap amounts
        const overlapX =
          Math.min(bounds1.x + bounds1.width, bounds2.x + bounds2.width) -
          Math.max(bounds1.x, bounds2.x);
        const overlapY =
          Math.min(bounds1.y + bounds1.height, bounds2.y + bounds2.height) -
          Math.max(bounds1.y, bounds2.y);

        // Determine separation direction (prefer smaller movement)
        const separateHorizontally = overlapX < overlapY;

        const adjustmentVector1 = { x: 0, y: 0 };
        const adjustmentVector2 = { x: 0, y: 0 };

        if (separateHorizontally) {
          const moveDistance = (overlapX + minSpacing) / 2;
          if (bounds1.x < bounds2.x) {
            adjustmentVector1.x = -moveDistance;
            adjustmentVector2.x = moveDistance;
          } else {
            adjustmentVector1.x = moveDistance;
            adjustmentVector2.x = -moveDistance;
          }
        } else {
          const moveDistance = (overlapY + minSpacing) / 2;
          if (bounds1.y < bounds2.y) {
            adjustmentVector1.y = -moveDistance;
            adjustmentVector2.y = moveDistance;
          } else {
            adjustmentVector1.y = moveDistance;
            adjustmentVector2.y = -moveDistance;
          }
        }

        collisions.push({
          nodeId1: node1.id,
          nodeId2: node2.id,
          adjustmentVector1,
          adjustmentVector2,
        });
      }
    }
  }

  return collisions;
};

/**
 * Enhanced edge-node collision detection with optimal direction finding
 */
const detectEdgeNodeCollisions = (
  nodes: Node[],
  edges: Edge[],
  positions: Map<string, { x: number; y: number }>,
  orientation: "horizontal" | "vertical",
  minSpacing: number,
): Array<{
  nodeId: string;
  edgeId: string;
  adjustmentVector: { x: number; y: number };
}> => {
  const collisions: Array<{
    nodeId: string;
    edgeId: string;
    adjustmentVector: { x: number; y: number };
  }> = [];

  // Check each edge against each node (except source and target)
  edges.forEach((edge) => {
    const sourcePos = positions.get(edge.source);
    const targetPos = positions.get(edge.target);

    if (!sourcePos || !targetPos) return;

    // Calculate edge center point for handle positions
    const sourceCenterX = sourcePos.x + DEFAULT_NODE_WIDTH / 2;
    const sourceCenterY = sourcePos.y + DEFAULT_NODE_HEIGHT / 2;
    const targetCenterX = targetPos.x + DEFAULT_NODE_WIDTH / 2;
    const targetCenterY = targetPos.y + DEFAULT_NODE_HEIGHT / 2;

    // Get edge path segments
    const segments = getEdgePathSegments(
      sourceCenterX,
      sourceCenterY,
      targetCenterX,
      targetCenterY,
      orientation,
    );

    // Check collision with each node (except source and target)
    nodes.forEach((node) => {
      if (node.id === edge.source || node.id === edge.target) return;

      // Use a much larger collision area to ensure minimum spacing
      const nodeBounds = getNodeBounds(node.id, positions, minSpacing + 15); // Larger buffer for better separation

      // Check if any edge segment intersects with the expanded node bounds
      const hasCollision = segments.some((segment) =>
        lineIntersectsRect(segment, nodeBounds),
      );

      if (hasCollision) {
        // Use optimal direction finding to determine best movement direction
        const { direction, clearance } = findOptimalDirection(
          node.id,
          positions,
          nodes,
          edges,
          orientation,
          minSpacing,
        );

        // Calculate adjustment vector based on optimal direction with stronger force
        const moveDistance = Math.max(
          minSpacing + 20, // Increased base movement
          minSpacing - clearance + 20, // Increased adjustment
        ); // Stronger separation force
        const adjustmentVector = { x: 0, y: 0 };

        switch (direction) {
          case "up":
            adjustmentVector.y = -moveDistance;
            break;
          case "down":
            adjustmentVector.y = moveDistance;
            break;
          case "left":
            adjustmentVector.x = -moveDistance;
            break;
          case "right":
            adjustmentVector.x = moveDistance;
            break;
        }

        collisions.push({
          nodeId: node.id,
          edgeId: edge.id,
          adjustmentVector,
        });
      }
    });
  });

  return collisions;
};

/**
 * Calculate the minimum distance between two line segments
 */
const calculateSegmentDistance = (
  seg1: { x1: number; y1: number; x2: number; y2: number },
  seg2: { x1: number; y1: number; x2: number; y2: number },
): number => {
  // Helper function to calculate distance from point to line segment
  const pointToSegmentDistance = (
    px: number,
    py: number,
    x1: number,
    y1: number,
    x2: number,
    y2: number,
  ): number => {
    const dx = x2 - x1;
    const dy = y2 - y1;
    const length = Math.sqrt(dx * dx + dy * dy);

    if (length === 0) {
      // Degenerate segment, return distance to point
      return Math.sqrt((px - x1) * (px - x1) + (py - y1) * (py - y1));
    }

    // Calculate the parameter t for the closest point on the line segment
    const t = Math.max(
      0,
      Math.min(1, ((px - x1) * dx + (py - y1) * dy) / (length * length)),
    );

    // Calculate the closest point on the segment
    const closestX = x1 + t * dx;
    const closestY = y1 + t * dy;

    // Return distance from point to closest point on segment
    return Math.sqrt(
      (px - closestX) * (px - closestX) + (py - closestY) * (py - closestY),
    );
  };

  // Calculate distances from each endpoint to the other segment
  const distances = [
    pointToSegmentDistance(
      seg1.x1,
      seg1.y1,
      seg2.x1,
      seg2.y1,
      seg2.x2,
      seg2.y2,
    ),
    pointToSegmentDistance(
      seg1.x2,
      seg1.y2,
      seg2.x1,
      seg2.y1,
      seg2.x2,
      seg2.y2,
    ),
    pointToSegmentDistance(
      seg2.x1,
      seg2.y1,
      seg1.x1,
      seg1.y1,
      seg1.x2,
      seg1.y2,
    ),
    pointToSegmentDistance(
      seg2.x2,
      seg2.y2,
      seg1.x1,
      seg1.y1,
      seg1.x2,
      seg1.y2,
    ),
  ];

  return Math.min(...distances);
};

/**
 * Calculate the minimum distance between two complete edges
 */
const calculateEdgeDistance = (
  edge1: Edge,
  edge2: Edge,
  positions: Map<string, { x: number; y: number }>,
  orientation: "horizontal" | "vertical",
): number => {
  const sourcePos1 = positions.get(edge1.source);
  const targetPos1 = positions.get(edge1.target);
  const sourcePos2 = positions.get(edge2.source);
  const targetPos2 = positions.get(edge2.target);

  if (!sourcePos1 || !targetPos1 || !sourcePos2 || !targetPos2) {
    return Infinity;
  }

  // Calculate edge center points for handle positions
  const sourceCenterX1 = sourcePos1.x + DEFAULT_NODE_WIDTH / 2;
  const sourceCenterY1 = sourcePos1.y + DEFAULT_NODE_HEIGHT / 2;
  const targetCenterX1 = targetPos1.x + DEFAULT_NODE_WIDTH / 2;
  const targetCenterY1 = targetPos1.y + DEFAULT_NODE_HEIGHT / 2;

  const sourceCenterX2 = sourcePos2.x + DEFAULT_NODE_WIDTH / 2;
  const sourceCenterY2 = sourcePos2.y + DEFAULT_NODE_HEIGHT / 2;
  const targetCenterX2 = targetPos2.x + DEFAULT_NODE_WIDTH / 2;
  const targetCenterY2 = targetPos2.y + DEFAULT_NODE_HEIGHT / 2;

  // Get edge path segments for both edges
  const segments1 = getEdgePathSegments(
    sourceCenterX1,
    sourceCenterY1,
    targetCenterX1,
    targetCenterY1,
    orientation,
  );

  const segments2 = getEdgePathSegments(
    sourceCenterX2,
    sourceCenterY2,
    targetCenterX2,
    targetCenterY2,
    orientation,
  );

  // Find minimum distance between any two segments from different edges
  let minDistance = Infinity;
  segments1.forEach((seg1) => {
    segments2.forEach((seg2) => {
      const distance = calculateSegmentDistance(seg1, seg2);
      minDistance = Math.min(minDistance, distance);
    });
  });

  return minDistance;
};

/**
 * Detect collisions between edges that have different source and target nodes
 */
const detectEdgeEdgeCollisions = (
  edges: Edge[],
  positions: Map<string, { x: number; y: number }>,
  orientation: "horizontal" | "vertical",
  minSpacing: number,
): Array<{
  edge1Id: string;
  edge2Id: string;
  distance: number;
  adjustmentVector1: { x: number; y: number };
  adjustmentVector2: { x: number; y: number };
}> => {
  const collisions: Array<{
    edge1Id: string;
    edge2Id: string;
    distance: number;
    adjustmentVector1: { x: number; y: number };
    adjustmentVector2: { x: number; y: number };
  }> = [];

  // Check each pair of edges
  for (let i = 0; i < edges.length; i++) {
    for (let j = i + 1; j < edges.length; j++) {
      const edge1 = edges[i];
      const edge2 = edges[j];

      // Only check edges with different source AND different target nodes
      if (edge1.source === edge2.source || edge1.target === edge2.target) {
        continue;
      }

      const distance = calculateEdgeDistance(
        edge1,
        edge2,
        positions,
        orientation,
      );

      if (distance < minSpacing) {
        // Calculate adjustment vectors to separate the edges
        const sourcePos1 = positions.get(edge1.source)!;
        const targetPos1 = positions.get(edge1.target)!;
        const sourcePos2 = positions.get(edge2.source)!;
        const targetPos2 = positions.get(edge2.target)!;

        // Calculate center points of both edges
        const center1X = (sourcePos1.x + targetPos1.x) / 2;
        const center1Y = (sourcePos1.y + targetPos1.y) / 2;
        const center2X = (sourcePos2.x + targetPos2.x) / 2;
        const center2Y = (sourcePos2.y + targetPos2.y) / 2;

        // Calculate separation vector
        const separationX = center2X - center1X;
        const separationY = center2Y - center1Y;
        const separationLength = Math.sqrt(
          separationX * separationX + separationY * separationY,
        );

        if (separationLength > 0) {
          const normalizedX = separationX / separationLength;
          const normalizedY = separationY / separationLength;

          // Calculate adjustment distance
          const adjustmentDistance = (minSpacing - distance) / 2 + 5; // Extra buffer

          collisions.push({
            edge1Id: edge1.id,
            edge2Id: edge2.id,
            distance,
            adjustmentVector1: {
              x: -normalizedX * adjustmentDistance,
              y: -normalizedY * adjustmentDistance,
            },
            adjustmentVector2: {
              x: normalizedX * adjustmentDistance,
              y: normalizedY * adjustmentDistance,
            },
          });
        }
      }
    }
  }

  return collisions;
};

/**
 * Unified collision resolution system
 */
const resolveAllCollisions = (
  nodes: Node[],
  edges: Edge[],
  positions: Map<string, { x: number; y: number }>,
  orientation: "horizontal" | "vertical",
  minSpacing: number,
  maxIterations: number = 10,
  edgeEdgeSpacing: number = 10,
): Map<string, { x: number; y: number }> => {
  const adjustedPositions = new Map(positions);

  for (let iteration = 0; iteration < maxIterations; iteration++) {
    let hasCollisions = false;

    // First, resolve node-node collisions
    const nodeCollisions = detectNodeNodeCollisions(
      nodes,
      adjustedPositions,
      minSpacing,
    );
    if (nodeCollisions.length > 0) {
      hasCollisions = true;

      const nodeAdjustments = new Map<string, { x: number; y: number }>();

      nodeCollisions.forEach(
        ({ nodeId1, nodeId2, adjustmentVector1, adjustmentVector2 }) => {
          const currentAdjustment1 = nodeAdjustments.get(nodeId1) || {
            x: 0,
            y: 0,
          };
          const currentAdjustment2 = nodeAdjustments.get(nodeId2) || {
            x: 0,
            y: 0,
          };

          nodeAdjustments.set(nodeId1, {
            x: currentAdjustment1.x + adjustmentVector1.x,
            y: currentAdjustment1.y + adjustmentVector1.y,
          });

          nodeAdjustments.set(nodeId2, {
            x: currentAdjustment2.x + adjustmentVector2.x,
            y: currentAdjustment2.y + adjustmentVector2.y,
          });
        },
      );

      // Apply node adjustments
      nodeAdjustments.forEach((adjustment, nodeId) => {
        const currentPos = adjustedPositions.get(nodeId);
        if (currentPos) {
          adjustedPositions.set(nodeId, {
            x: currentPos.x + adjustment.x,
            y: currentPos.y + adjustment.y,
          });
        }
      });
    }

    // Then, resolve edge-node collisions
    const edgeCollisions = detectEdgeNodeCollisions(
      nodes,
      edges,
      adjustedPositions,
      orientation,
      minSpacing,
    );

    if (edgeCollisions.length > 0) {
      hasCollisions = true;

      const edgeAdjustments = new Map<string, { x: number; y: number }>();

      edgeCollisions.forEach(({ nodeId, adjustmentVector }) => {
        const currentAdjustment = edgeAdjustments.get(nodeId) || { x: 0, y: 0 };
        edgeAdjustments.set(nodeId, {
          x: currentAdjustment.x + adjustmentVector.x,
          y: currentAdjustment.y + adjustmentVector.y,
        });
      });

      // Apply edge adjustments
      edgeAdjustments.forEach((adjustment, nodeId) => {
        const currentPos = adjustedPositions.get(nodeId);
        if (currentPos) {
          adjustedPositions.set(nodeId, {
            x: currentPos.x + adjustment.x,
            y: currentPos.y + adjustment.y,
          });
        }
      });
    }

    // Finally, resolve edge-edge collisions
    const edgeEdgeCollisions = detectEdgeEdgeCollisions(
      edges,
      adjustedPositions,
      orientation,
      edgeEdgeSpacing,
    );

    if (edgeEdgeCollisions.length > 0) {
      hasCollisions = true;

      const edgeEdgeAdjustments = new Map<string, { x: number; y: number }>();

      edgeEdgeCollisions.forEach(
        ({ edge1Id, edge2Id, adjustmentVector1, adjustmentVector2 }) => {
          // Find the edges to get their source and target nodes
          const edge1 = edges.find((e) => e.id === edge1Id);
          const edge2 = edges.find((e) => e.id === edge2Id);

          if (edge1 && edge2) {
            // Apply adjustments to both source and target nodes of each edge
            [edge1.source, edge1.target].forEach((nodeId) => {
              const currentAdjustment = edgeEdgeAdjustments.get(nodeId) || {
                x: 0,
                y: 0,
              };
              edgeEdgeAdjustments.set(nodeId, {
                x: currentAdjustment.x + adjustmentVector1.x * 0.5, // Reduce adjustment to prevent overcorrection
                y: currentAdjustment.y + adjustmentVector1.y * 0.5,
              });
            });

            [edge2.source, edge2.target].forEach((nodeId) => {
              const currentAdjustment = edgeEdgeAdjustments.get(nodeId) || {
                x: 0,
                y: 0,
              };
              edgeEdgeAdjustments.set(nodeId, {
                x: currentAdjustment.x + adjustmentVector2.x * 0.5, // Reduce adjustment to prevent overcorrection
                y: currentAdjustment.y + adjustmentVector2.y * 0.5,
              });
            });
          }
        },
      );

      // Apply edge-edge adjustments
      edgeEdgeAdjustments.forEach((adjustment, nodeId) => {
        const currentPos = adjustedPositions.get(nodeId);
        if (currentPos) {
          adjustedPositions.set(nodeId, {
            x: currentPos.x + adjustment.x,
            y: currentPos.y + adjustment.y,
          });
        }
      });
    }

    // If no collisions found, we're done
    if (!hasCollisions) break;
  }

  return adjustedPositions;
};

/**
 * Get closest point on a line segment to a given point
 */
const getClosestPointOnLine = (
  point: { x: number; y: number },
  line: { x1: number; y1: number; x2: number; y2: number },
): { x: number; y: number } => {
  const { x, y } = point;
  const { x1, y1, x2, y2 } = line;

  const dx = x2 - x1;
  const dy = y2 - y1;
  const length = dx * dx + dy * dy;

  if (length === 0) return { x: x1, y: y1 };

  const t = Math.max(0, Math.min(1, ((x - x1) * dx + (y - y1) * dy) / length));

  return {
    x: x1 + t * dx,
    y: y1 + t * dy,
  };
};

/**
 * Calculates auto-layout positions for nodes with improved spacing and alignment
 */
export const calculateAutoLayout = (
  nodes: Node[],
  edges: Edge[],
  options: LayoutOptions = {},
): AutoLayoutResult => {
  const {
    horizontalSpacing = 300,
    verticalSpacing = 200,
    startX = 100,
    startY = 100,
    alignByLayer = true,
    handleOrientation = "auto",
    minimizeEdgeCrossings = true,
    edgeCrossingIterations = 3,
    dynamicSpacing = true,
    edgeNodeSpacing = 50,
    edgeNodeCollisionIterations = 3,
    edgeEdgeSpacing = 10,
    edgeEdgeCollisionIterations = 5,
  } = options;

  if (nodes.length === 0) {
    return { positions: new Map(), orientation: "horizontal" };
  }

  const orientation =
    handleOrientation === "auto"
      ? detectHandleOrientation(nodes)
      : handleOrientation;

  // Create adjacency list and calculate in-degrees
  const adjacencyList = new Map<string, string[]>();
  const reverseAdjacencyList = new Map<string, string[]>();
  const inDegree = new Map<string, number>();

  // Initialize
  nodes.forEach((node) => {
    adjacencyList.set(node.id, []);
    reverseAdjacencyList.set(node.id, []);
    inDegree.set(node.id, 0);
  });

  // Build graph
  edges.forEach((edge) => {
    if (edge.source && edge.target) {
      adjacencyList.get(edge.source)?.push(edge.target);
      reverseAdjacencyList.get(edge.target)?.push(edge.source);
      inDegree.set(edge.target, (inDegree.get(edge.target) || 0) + 1);
    }
  });

  // Topological sort to determine levels
  const levels = new Map<string, number>();
  const levelNodes = new Map<number, string[]>();
  const queue: string[] = [];

  // Find root nodes (nodes with no incoming edges)
  nodes.forEach((node) => {
    if (inDegree.get(node.id) === 0) {
      queue.push(node.id);
      levels.set(node.id, 0);
    }
  });

  // If no root nodes, use the first node as root
  if (queue.length === 0 && nodes.length > 0) {
    const firstNode = nodes[0];
    queue.push(firstNode.id);
    levels.set(firstNode.id, 0);
  }

  // BFS to assign levels
  while (queue.length > 0) {
    const nodeId = queue.shift()!;
    const currentLevel = levels.get(nodeId) || 0;

    // Add to level tracking
    if (!levelNodes.has(currentLevel)) {
      levelNodes.set(currentLevel, []);
    }
    levelNodes.get(currentLevel)!.push(nodeId);

    // Process children
    const children = adjacencyList.get(nodeId) || [];
    children.forEach((childId) => {
      const childLevel = Math.max(levels.get(childId) || 0, currentLevel + 1);
      levels.set(childId, childLevel);

      // Decrease in-degree and add to queue if ready
      const newInDegree = (inDegree.get(childId) || 0) - 1;
      inDegree.set(childId, newInDegree);

      if (newInDegree === 0) {
        queue.push(childId);
      }
    });
  }

  // Handle any remaining nodes (cycles or disconnected components)
  nodes.forEach((node) => {
    if (!levels.has(node.id)) {
      const maxLevel = Math.max(...Array.from(levels.values()), -1);
      levels.set(node.id, maxLevel + 1);

      const level = maxLevel + 1;
      if (!levelNodes.has(level)) {
        levelNodes.set(level, []);
      }
      levelNodes.get(level)!.push(node.id);
    }
  });

  // Apply edge crossing minimization if enabled
  let optimizedLevelNodes = levelNodes;
  if (minimizeEdgeCrossings && edges.length > 0) {
    optimizedLevelNodes = minimizeEdgeCrossingsWithBarycenter(
      levelNodes,
      adjacencyList,
      reverseAdjacencyList,
      orientation,
      edgeCrossingIterations,
    );
  }

  // Calculate dynamic spacing if enabled
  const finalHorizontalSpacing = dynamicSpacing
    ? calculateDynamicSpacing(
        optimizedLevelNodes,
        adjacencyList,
        horizontalSpacing,
        orientation,
      )
    : horizontalSpacing;
  const finalVerticalSpacing = dynamicSpacing
    ? calculateDynamicSpacing(
        optimizedLevelNodes,
        adjacencyList,
        verticalSpacing,
        orientation,
      )
    : verticalSpacing;

  // Calculate positions based on orientation
  const positions = new Map<string, { x: number; y: number }>();

  if (orientation === "horizontal") {
    // Horizontal layout (left to right)
    optimizedLevelNodes.forEach((nodeIds, level) => {
      const totalNodesInLevel = nodeIds.length;
      const levelHeight = (totalNodesInLevel - 1) * finalVerticalSpacing;
      const startYForLevel = startY - levelHeight / 2;

      nodeIds.forEach((nodeId, indexInLevel) => {
        positions.set(nodeId, {
          x: startX + level * finalHorizontalSpacing,
          y: startYForLevel + indexInLevel * finalVerticalSpacing,
        });
      });
    });
  } else {
    // Vertical layout (top to bottom)
    optimizedLevelNodes.forEach((nodeIds, level) => {
      const totalNodesInLevel = nodeIds.length;
      const levelWidth = (totalNodesInLevel - 1) * finalHorizontalSpacing;
      const startXForLevel = startX - levelWidth / 2;

      nodeIds.forEach((nodeId, indexInLevel) => {
        positions.set(nodeId, {
          x: startXForLevel + indexInLevel * finalHorizontalSpacing,
          y: startY + level * finalVerticalSpacing,
        });
      });
    });
  }

  // Apply unified collision detection and resolution (node-node, edge-node, and edge-edge)
  if (edgeNodeSpacing > 0 || edgeEdgeSpacing > 0) {
    const adjustedPositions = resolveAllCollisions(
      nodes,
      edges,
      positions,
      orientation,
      Math.max(edgeNodeSpacing, 10), // Ensure minimum 10px spacing as requested
      Math.max(edgeNodeCollisionIterations, edgeEdgeCollisionIterations),
      Math.max(edgeEdgeSpacing, 10), // Ensure minimum 10px edge-edge spacing
    );

    return { positions: adjustedPositions, orientation };
  }

  return { positions, orientation };
};

/**
 * Easing function for smooth animations
 */
export const easeOutCubic = (t: number): number => 1 - (1 - t) ** 3;

/**
 * Enhanced auto-layout function with smooth animations
 */
export const applyAutoLayoutSmooth = (
  nodes: Node[],
  edges: Edge[],
  updateNodes: (nodes: Node[]) => void,
  fitView?: (options?: { padding?: number; duration?: number }) => void,
  options: LayoutOptions = {},
): void => {
  const { animationDuration = 500, onComplete, ...layoutOptions } = options;

  if (nodes.length === 0) return;

  const { positions: targetPositions, orientation } = calculateAutoLayout(
    nodes,
    edges,
    layoutOptions,
  );

  if (targetPositions.size === 0) return;

  // Store initial positions
  const initialPositions = new Map<string, { x: number; y: number }>();
  nodes.forEach((node) => {
    initialPositions.set(node.id, { x: node.position.x, y: node.position.y });
  });

  const startTime = Date.now();

  const animate = () => {
    const elapsed = Date.now() - startTime;
    const progress = Math.min(elapsed / animationDuration, 1);
    const easedProgress = easeOutCubic(progress);

    // Update node positions
    const updatedNodes = nodes.map((node) => {
      const initialPos = initialPositions.get(node.id);
      const targetPos = targetPositions.get(node.id);

      if (!initialPos || !targetPos) return node;

      const newPosition = {
        x: initialPos.x + (targetPos.x - initialPos.x) * easedProgress,
        y: initialPos.y + (targetPos.y - initialPos.y) * easedProgress,
      };

      return {
        ...node,
        position: newPosition,
      };
    });

    updateNodes(updatedNodes);

    if (progress < 1) {
      requestAnimationFrame(animate);
    } else {
      // Animation complete
      if (fitView) {
        fitView({
          padding: 0.2,
          duration: 400,
        });
      }

      if (onComplete) {
        onComplete(targetPositions);
      }
    }
  };

  animate();
};

/**
 * Animate node positions from current to target positions
 * Used for undo/redo operations to provide smooth transitions
 */
export const animateNodePositions = (
  currentNodes: Node[],
  targetNodes: Node[],
  updateNodes: (nodes: Node[]) => void,
  options: { animationDuration?: number; onComplete?: () => void } = {},
): void => {
  const { animationDuration = 500, onComplete } = options;

  if (currentNodes.length === 0 || targetNodes.length === 0) {
    updateNodes(targetNodes);
    if (onComplete) onComplete();
    return;
  }

  // Create maps for quick lookup
  const currentNodeMap = new Map<string, Node>();
  const targetNodeMap = new Map<string, Node>();

  currentNodes.forEach((node) => currentNodeMap.set(node.id, node));
  targetNodes.forEach((node) => targetNodeMap.set(node.id, node));

  // Check if any nodes actually need position animation
  let hasPositionChanges = false;
  for (const targetNode of targetNodes) {
    const currentNode = currentNodeMap.get(targetNode.id);
    if (
      currentNode &&
      (Math.abs(currentNode.position.x - targetNode.position.x) > 0.1 ||
        Math.abs(currentNode.position.y - targetNode.position.y) > 0.1)
    ) {
      hasPositionChanges = true;
      break;
    }
  }

  // If no position changes, update immediately
  if (!hasPositionChanges) {
    updateNodes(targetNodes);
    if (onComplete) onComplete();
    return;
  }

  const startTime = Date.now();

  const animate = () => {
    const elapsed = Date.now() - startTime;
    const progress = Math.min(elapsed / animationDuration, 1);
    const easedProgress = easeOutCubic(progress);

    // Create animated nodes
    const animatedNodes = targetNodes.map((targetNode) => {
      const currentNode = currentNodeMap.get(targetNode.id);

      // If node doesn't exist in current state, use target position directly
      if (!currentNode) {
        return targetNode;
      }

      // Interpolate position
      const newPosition = {
        x:
          currentNode.position.x +
          (targetNode.position.x - currentNode.position.x) * easedProgress,
        y:
          currentNode.position.y +
          (targetNode.position.y - currentNode.position.y) * easedProgress,
      };

      return {
        ...targetNode,
        position: newPosition,
      };
    });

    updateNodes(animatedNodes);

    if (progress < 1) {
      requestAnimationFrame(animate);
    } else {
      // Animation complete - ensure final positions are exact
      updateNodes(targetNodes);
      if (onComplete) {
        onComplete();
      }
    }
  };

  animate();
};

/**
 * Debounced auto layout to prevent rapid triggering
 */
export const createDebouncedAutoLayout = (
  autoLayoutFn: () => void,
  delay: number = 250,
) => {
  let timeoutId: NodeJS.Timeout | null = null;

  return () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      autoLayoutFn();
      timeoutId = null;
    }, delay);

    // Return cleanup function
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
    };
  };
};
