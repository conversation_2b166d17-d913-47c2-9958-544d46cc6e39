import type { Edge, Node } from "@xyflow/react";
import { animateNodePositions, calculateAutoLayout } from "../autoLayout";

describe("AutoLayout Edge Overlap Prevention", () => {
  // Helper function to create test nodes
  const createNode = (id: string): Node => ({
    id,
    type: "custom",
    position: { x: 0, y: 0 },
    data: { label: `Node ${id}` },
  });

  // Helper function to create test edges
  const createEdge = (source: string, target: string): Edge => ({
    id: `${source}-${target}`,
    source,
    target,
  });

  test("should minimize edge crossings with barycenter heuristic", () => {
    // Create a test graph that would have edge crossings without optimization
    const nodes: Node[] = [
      createNode("A"),
      createNode("B"),
      createNode("C"),
      createNode("D"),
      createNode("E"),
      createNode("F"),
    ];

    const edges: Edge[] = [
      createEdge("A", "D"),
      createEdge("A", "E"),
      createEdge("B", "D"),
      createEdge("B", "F"),
      createEdge("C", "E"),
      createEdge("C", "F"),
    ];

    const result = calculateAutoLayout(nodes, edges, {
      minimizeEdgeCrossings: true,
      edgeCrossingIterations: 3,
      horizontalSpacing: 300,
      verticalSpacing: 200,
    });

    expect(result.positions.size).toBe(6);
    expect(result.orientation).toBe("horizontal");

    // Verify that nodes are positioned
    const positionA = result.positions.get("A");
    const positionD = result.positions.get("D");

    expect(positionA).toBeDefined();
    expect(positionD).toBeDefined();

    if (positionA && positionD) {
      // In horizontal layout, target nodes should be to the right of source nodes
      expect(positionD.x).toBeGreaterThan(positionA.x);
    }
  });

  test("should apply dynamic spacing based on edge density", () => {
    // Create a graph with high edge density
    const nodes: Node[] = [
      createNode("A"),
      createNode("B"),
      createNode("C"),
      createNode("D"),
      createNode("E"),
      createNode("F"),
    ];

    const edges: Edge[] = [
      createEdge("A", "D"),
      createEdge("A", "E"),
      createEdge("A", "F"),
      createEdge("B", "D"),
      createEdge("B", "E"),
      createEdge("B", "F"),
      createEdge("C", "D"),
      createEdge("C", "E"),
      createEdge("C", "F"),
    ];

    const resultWithDynamicSpacing = calculateAutoLayout(nodes, edges, {
      dynamicSpacing: true,
      horizontalSpacing: 300,
      verticalSpacing: 200,
    });

    const resultWithoutDynamicSpacing = calculateAutoLayout(nodes, edges, {
      dynamicSpacing: false,
      horizontalSpacing: 300,
      verticalSpacing: 200,
    });

    expect(resultWithDynamicSpacing.positions.size).toBe(6);
    expect(resultWithoutDynamicSpacing.positions.size).toBe(6);

    // Both should position all nodes
    const positionA1 = resultWithDynamicSpacing.positions.get("A");
    const positionA2 = resultWithoutDynamicSpacing.positions.get("A");

    expect(positionA1).toBeDefined();
    expect(positionA2).toBeDefined();
  });

  test("should handle empty graph gracefully", () => {
    const result = calculateAutoLayout([], [], {
      minimizeEdgeCrossings: true,
      dynamicSpacing: true,
    });

    expect(result.positions.size).toBe(0);
    expect(result.orientation).toBe("horizontal");
  });

  test("should handle single node gracefully", () => {
    const nodes: Node[] = [createNode("A")];
    const edges: Edge[] = [];

    const result = calculateAutoLayout(nodes, edges, {
      minimizeEdgeCrossings: true,
      dynamicSpacing: true,
    });

    expect(result.positions.size).toBe(1);
    expect(result.positions.get("A")).toBeDefined();
  });

  test("should handle disconnected components", () => {
    const nodes: Node[] = [
      createNode("A"),
      createNode("B"),
      createNode("C"),
      createNode("D"),
    ];

    const edges: Edge[] = [createEdge("A", "B"), createEdge("C", "D")];

    const result = calculateAutoLayout(nodes, edges, {
      minimizeEdgeCrossings: true,
      dynamicSpacing: true,
    });

    expect(result.positions.size).toBe(4);

    // All nodes should be positioned
    nodes.forEach((node) => {
      expect(result.positions.get(node.id)).toBeDefined();
    });
  });

  test("should respect orientation setting", () => {
    const nodes: Node[] = [createNode("A"), createNode("B")];

    const edges: Edge[] = [createEdge("A", "B")];

    const horizontalResult = calculateAutoLayout(nodes, edges, {
      handleOrientation: "horizontal",
    });

    const verticalResult = calculateAutoLayout(nodes, edges, {
      handleOrientation: "vertical",
    });

    expect(horizontalResult.orientation).toBe("horizontal");
    expect(verticalResult.orientation).toBe("vertical");
  });

  test("should prevent node-node overlaps with minimum spacing", () => {
    // Create nodes that would naturally overlap
    const nodes: Node[] = [createNode("A"), createNode("B"), createNode("C")];

    const edges: Edge[] = [createEdge("A", "B"), createEdge("B", "C")];

    const result = calculateAutoLayout(nodes, edges, {
      edgeNodeSpacing: 10, // Minimum 10px spacing
      edgeNodeCollisionIterations: 5,
      horizontalSpacing: 50, // Small spacing to force potential overlaps
      verticalSpacing: 50,
    });

    expect(result.positions.size).toBe(3);

    // Check that no two nodes overlap
    const positions = Array.from(result.positions.entries());
    for (let i = 0; i < positions.length; i++) {
      for (let j = i + 1; j < positions.length; j++) {
        const [nodeId1, pos1] = positions[i];
        const [nodeId2, pos2] = positions[j];

        // Calculate distance between node centers
        const distance = Math.sqrt(
          Math.pow(pos2.x - pos1.x, 2) + Math.pow(pos2.y - pos1.y, 2),
        );

        // Nodes should be at least 10px apart (plus node dimensions)
        expect(distance).toBeGreaterThan(10);
      }
    }
  });

  test("should prevent edge-node overlaps with optimal direction finding", () => {
    // Create a scenario where a node would overlap with an edge
    const nodes: Node[] = [
      createNode("A"),
      createNode("B"),
      createNode("C"), // This node should be positioned to avoid the A-B edge
      createNode("D"),
    ];

    const edges: Edge[] = [createEdge("A", "B"), createEdge("C", "D")];

    const result = calculateAutoLayout(nodes, edges, {
      edgeNodeSpacing: 10,
      edgeNodeCollisionIterations: 5,
      horizontalSpacing: 100,
      verticalSpacing: 100,
    });

    expect(result.positions.size).toBe(4);

    // All nodes should be positioned
    nodes.forEach((node) => {
      const position = result.positions.get(node.id);
      expect(position).toBeDefined();
      expect(position!.x).toBeGreaterThanOrEqual(0);
      expect(position!.y).toBeGreaterThanOrEqual(0);
    });
  });

  test("should handle complex graphs with multiple collision types", () => {
    // Create a complex graph with potential for both node-node and edge-node collisions
    const nodes: Node[] = [
      createNode("A"),
      createNode("B"),
      createNode("C"),
      createNode("D"),
      createNode("E"),
      createNode("F"),
      createNode("G"),
    ];

    const edges: Edge[] = [
      createEdge("A", "D"),
      createEdge("B", "E"),
      createEdge("C", "F"),
      createEdge("D", "G"),
      createEdge("E", "G"),
      createEdge("F", "G"),
    ];

    const result = calculateAutoLayout(nodes, edges, {
      edgeNodeSpacing: 10,
      edgeNodeCollisionIterations: 10,
      horizontalSpacing: 150,
      verticalSpacing: 100,
      minimizeEdgeCrossings: true,
      dynamicSpacing: true,
    });

    expect(result.positions.size).toBe(7);

    // Verify all nodes are positioned with reasonable spacing
    const positions = Array.from(result.positions.values());
    positions.forEach((pos) => {
      expect(pos.x).toBeGreaterThanOrEqual(0);
      expect(pos.y).toBeGreaterThanOrEqual(0);
    });

    // Check that nodes maintain minimum distance
    for (let i = 0; i < positions.length; i++) {
      for (let j = i + 1; j < positions.length; j++) {
        const distance = Math.sqrt(
          Math.pow(positions[j].x - positions[i].x, 2) +
            Math.pow(positions[j].y - positions[i].y, 2),
        );
        expect(distance).toBeGreaterThan(5); // Some minimum distance
      }
    }
  });

  test("should maintain minimum 10px spacing as requested", () => {
    const nodes: Node[] = [createNode("A"), createNode("B")];

    const edges: Edge[] = [createEdge("A", "B")];

    const result = calculateAutoLayout(nodes, edges, {
      edgeNodeSpacing: 10,
      horizontalSpacing: 50, // Force close positioning
      verticalSpacing: 50,
    });

    const posA = result.positions.get("A")!;
    const posB = result.positions.get("B")!;

    // Calculate actual distance between nodes
    const distance = Math.sqrt(
      Math.pow(posB.x - posA.x, 2) + Math.pow(posB.y - posA.y, 2),
    );

    // Should maintain at least 10px spacing
    expect(distance).toBeGreaterThanOrEqual(10);
  });
});

describe("AnimateNodePositions", () => {
  // Helper function to create test nodes
  const createNode = (id: string, x: number, y: number): Node => ({
    id,
    type: "custom",
    position: { x, y },
    data: { label: `Node ${id}` },
  });

  test("should call updateNodes with target positions when no animation needed", () => {
    const currentNodes = [createNode("A", 100, 100)];
    const targetNodes = [createNode("A", 100, 100)]; // Same position
    const mockUpdateNodes = jest.fn();

    animateNodePositions(currentNodes, targetNodes, mockUpdateNodes, {
      animationDuration: 100,
    });

    // Should call updateNodes immediately with target nodes
    expect(mockUpdateNodes).toHaveBeenCalledWith(targetNodes);
  });

  test("should handle empty node arrays", () => {
    const mockUpdateNodes = jest.fn();
    const mockOnComplete = jest.fn();

    animateNodePositions([], [], mockUpdateNodes, {
      onComplete: mockOnComplete,
    });

    expect(mockUpdateNodes).toHaveBeenCalledWith([]);
    expect(mockOnComplete).toHaveBeenCalled();
  });

  test("should detect position changes correctly", () => {
    const currentNodes = [createNode("A", 100, 100)];
    const targetNodes = [createNode("A", 200, 200)]; // Different position
    const mockUpdateNodes = jest.fn();

    animateNodePositions(currentNodes, targetNodes, mockUpdateNodes, {
      animationDuration: 100,
    });

    // Should start animation, so updateNodes will be called multiple times
    expect(mockUpdateNodes).toHaveBeenCalled();
  });
});
