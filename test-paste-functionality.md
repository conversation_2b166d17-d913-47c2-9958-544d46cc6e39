# Test Paste Functionality

## Steps to Test:

1. **Open the editor**: Navigate to http://localhost:3001/editor
2. **Select a node**: Click on any node to select it (it should highlight)
3. **Copy the node**: Press Ctrl+C (or Cmd+C on Mac)
4. **Move your mouse**: Move your mouse cursor to a different position on the canvas
5. **Paste the node**: Press Ctrl+V (or Cmd+V on Mac)

## Expected Behavior:

1. ✅ The node should be pasted at the current mouse cursor position
2. ✅ The pasted node should automatically connect to the nearest existing node
3. ✅ An edge should be created from the nearest node's exit point to the pasted node's entry point
4. ✅ The connection should only happen if there's a node within 800px distance
5. ✅ Console should show auto-connection messages

## Implementation Details:

- Mouse position is tracked in real-time using `onMouseMove` handler
- Position is converted from screen coordinates to flow coordinates
- Paste function receives the current mouse position
- Auto-connection logic finds the closest node within 800px
- Creates an edge from closest node's exit to pasted node's entry

## Console Messages to Look For:

- `🔗 Auto-connecting pasted node "NodeName" to closest node "ClosestNodeName"`
- `ℹ️ No suitable node found for auto-connection (all nodes too far from paste position)`
